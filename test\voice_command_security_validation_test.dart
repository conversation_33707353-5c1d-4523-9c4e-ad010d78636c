import 'package:flutter_test/flutter_test.dart';
import 'package:phoenix/features/security_list/model/security_model.dart';

/// Test helper class to test security validation logic
class SecurityValidationTestHelper {
  /// Validate if a security is valid for trading
  /// This mirrors the logic from VoiceCommandHandler._isSecurityValidForTrading
  static bool isSecurityValidForTrading(SecurityModel security) {
    // Check if zenId is valid (should be positive)
    if (security.zenId <= 0) {
      return false;
    }

    // Check if trading symbol is valid (should not be empty or "Not Found")
    if (security.tradingSymbol.isEmpty || 
        security.tradingSymbol.toUpperCase() == 'NOT FOUND') {
      return false;
    }

    // Check if name is valid (should not be empty or "Not Found")
    if (security.name.isEmpty || 
        security.name.toUpperCase() == 'NOT FOUND') {
      return false;
    }

    // Check if exchanges list is not empty
    if (security.exchanges.isEmpty) {
      return false;
    }

    // Check if instrument type is valid (should not be empty)
    if (security.instrumentType.isEmpty) {
      return false;
    }

    // Check if lot size is valid (should be positive)
    if (security.lotSize <= 0) {
      return false;
    }

    // All validations passed
    return true;
  }
}

void main() {
  group('Security Validation Tests', () {
    test('Valid security should pass validation', () {
      final validSecurity = SecurityModel(
        zenId: 123,
        tradingSymbol: 'RELIANCE',
        name: 'Reliance Industries Limited',
        strike: 0.0,
        exchanges: ['NSE', 'BSE'],
        lotSize: 1,
        instrumentType: 'EQ',
        expiryType: null,
        expiry: null,
      );

      expect(SecurityValidationTestHelper.isSecurityValidForTrading(validSecurity), true);
    });

    test('Security with invalid zenId should fail validation', () {
      final invalidSecurity = SecurityModel(
        zenId: 0, // Invalid zenId
        tradingSymbol: 'RELIANCE',
        name: 'Reliance Industries Limited',
        strike: 0.0,
        exchanges: ['NSE', 'BSE'],
        lotSize: 1,
        instrumentType: 'EQ',
        expiryType: null,
        expiry: null,
      );

      expect(SecurityValidationTestHelper.isSecurityValidForTrading(invalidSecurity), false);
    });

    test('Security with "Not Found" trading symbol should fail validation', () {
      final invalidSecurity = SecurityModel(
        zenId: 123,
        tradingSymbol: 'Not Found', // Invalid trading symbol
        name: 'Reliance Industries Limited',
        strike: 0.0,
        exchanges: ['NSE', 'BSE'],
        lotSize: 1,
        instrumentType: 'EQ',
        expiryType: null,
        expiry: null,
      );

      expect(SecurityValidationTestHelper.isSecurityValidForTrading(invalidSecurity), false);
    });

    test('Security with empty trading symbol should fail validation', () {
      final invalidSecurity = SecurityModel(
        zenId: 123,
        tradingSymbol: '', // Empty trading symbol
        name: 'Reliance Industries Limited',
        strike: 0.0,
        exchanges: ['NSE', 'BSE'],
        lotSize: 1,
        instrumentType: 'EQ',
        expiryType: null,
        expiry: null,
      );

      expect(SecurityValidationTestHelper.isSecurityValidForTrading(invalidSecurity), false);
    });

    test('Security with "Not Found" name should fail validation', () {
      final invalidSecurity = SecurityModel(
        zenId: 123,
        tradingSymbol: 'RELIANCE',
        name: 'Not Found', // Invalid name
        strike: 0.0,
        exchanges: ['NSE', 'BSE'],
        lotSize: 1,
        instrumentType: 'EQ',
        expiryType: null,
        expiry: null,
      );

      expect(SecurityValidationTestHelper.isSecurityValidForTrading(invalidSecurity), false);
    });

    test('Security with empty exchanges should fail validation', () {
      final invalidSecurity = SecurityModel(
        zenId: 123,
        tradingSymbol: 'RELIANCE',
        name: 'Reliance Industries Limited',
        strike: 0.0,
        exchanges: [], // Empty exchanges
        lotSize: 1,
        instrumentType: 'EQ',
        expiryType: null,
        expiry: null,
      );

      expect(SecurityValidationTestHelper.isSecurityValidForTrading(invalidSecurity), false);
    });

    test('Security with empty instrument type should fail validation', () {
      final invalidSecurity = SecurityModel(
        zenId: 123,
        tradingSymbol: 'RELIANCE',
        name: 'Reliance Industries Limited',
        strike: 0.0,
        exchanges: ['NSE', 'BSE'],
        lotSize: 1,
        instrumentType: '', // Empty instrument type
        expiryType: null,
        expiry: null,
      );

      expect(SecurityValidationTestHelper.isSecurityValidForTrading(invalidSecurity), false);
    });

    test('Security with invalid lot size should fail validation', () {
      final invalidSecurity = SecurityModel(
        zenId: 123,
        tradingSymbol: 'RELIANCE',
        name: 'Reliance Industries Limited',
        strike: 0.0,
        exchanges: ['NSE', 'BSE'],
        lotSize: 0, // Invalid lot size
        instrumentType: 'EQ',
        expiryType: null,
        expiry: null,
      );

      expect(SecurityValidationTestHelper.isSecurityValidForTrading(invalidSecurity), false);
    });

    test('Valid F&O security should pass validation', () {
      final validFnoSecurity = SecurityModel(
        zenId: 456,
        tradingSymbol: 'NIFTY24DEC21000CE',
        name: 'NIFTY 21 Dec 2024 21000 CE',
        strike: 21000.0,
        exchanges: ['NFO'],
        lotSize: 25,
        instrumentType: 'CE',
        expiryType: 'WEEKLY',
        expiry: '2024-12-21',
      );

      expect(SecurityValidationTestHelper.isSecurityValidForTrading(validFnoSecurity), true);
    });
  });
}

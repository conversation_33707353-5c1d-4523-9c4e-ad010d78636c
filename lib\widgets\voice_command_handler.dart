import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/conversation/bloc/conversation_event.dart';
import 'package:phoenix/services/voice_assistant_service.dart';
import 'package:phoenix/models/voice_command_model.dart';
import 'package:phoenix/features/orders/bloc/orders_bloc.dart';
import 'package:phoenix/features/orders/model/order_form_model.dart';
import 'package:phoenix/features/portfolio_data/bloc/portfolio_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/features/security_list/bloc/security_list_bloc.dart';
import 'package:phoenix/features/security_list/model/security_model.dart';
import 'package:phoenix/services/security_list_search_service.dart';
import 'package:phoenix/utils/http_service.dart';
import 'package:phoenix/utils/api_path.dart';
import 'package:phoenix/features/conversation/bloc/conversation_bloc.dart';
import 'package:phoenix/features/broker_account_strategy_selection/bloc/broker_account_strategy_selection_bloc.dart';
import 'package:phoenix/widgets/broker_account_strategy_selector/broker_account_strategy_modal.dart';
import 'dart:convert';

/// Widget that handles voice commands and integrates with the app's order system
class VoiceCommandHandler extends StatefulWidget {
  final Widget child;

  const VoiceCommandHandler({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  State<VoiceCommandHandler> createState() => _VoiceCommandHandlerState();
}

class _VoiceCommandHandlerState extends State<VoiceCommandHandler> {
  final VoiceAssistantService _voiceAssistant = VoiceAssistantService();
  final HttpService _httpService = HttpService();

  @override
  void initState() {
    debugPrint('🎙️ VoiceCommandHandler: Init ran');
    super.initState();
    _initializeVoiceAssistant();
    _setupVoiceCommandListener();
  }

  Future<void> _initializeVoiceAssistant() async {
    // The VoiceAssistantService is already initialized by ConversationBloc
    // Just ensure security list is loaded for voice commands
    await _ensureSecurityListLoaded();
  }

  Future<void> _ensureSecurityListLoaded() async {
    final securityState = context.read<SecurityListBloc>().state;
    if (securityState is! SecurityListLoaded) {
      debugPrint('VoiceCommandHandler: Security list not loaded, fetching...');

      // Start the fetch
      context.read<SecurityListBloc>().add(FetchSecurityListEvent());

      // Wait for it to load (with timeout)
      int attempts = 0;
      while (attempts < 10) {
        // Max 5 seconds wait
        await Future.delayed(const Duration(milliseconds: 500));
        final currentState = context.read<SecurityListBloc>().state;
        if (currentState is SecurityListLoaded) {
          debugPrint(
              'VoiceCommandHandler: Security list loaded with ${currentState.equityList.length} equities, ${currentState.featuresList.length} F&O');
          return;
        }
        attempts++;
      }
      debugPrint(
          'VoiceCommandHandler: Timeout waiting for security list to load');
    } else {
      debugPrint(
          'VoiceCommandHandler: Security list already loaded with ${securityState.equityList.length} equities, ${securityState.featuresList.length} F&O');
    }
  }

  void _setupVoiceCommandListener() {
    _voiceAssistant.commandStream.listen((command) {
      // Guard: if a conversation voice session is active (listening/paused/finalizing/processing),
      // ignore background/early commands to prevent duplicate or premature actions.
      final conversationBloc = context.read<ConversationBloc>();
      final isActiveSession = conversationBloc.state.isInVoiceSession;
      // if (isActiveSession) {
      //   debugPrint('VoiceCommandHandler: Ignoring command during active voice session: ${command.toString()}');
      //   return;
      // }
      _handleVoiceCommand(command);
    });
  }

  Future<void> _handleVoiceCommand(VoiceCommand command) async {
    debugPrint(
        'VoiceCommandHandler: Received voice command: ${command.toString()}');
    debugPrint(
        'VoiceCommandHandler: Action: ${command.action}, Symbol: ${command.symbol}, Quantity: ${command.quantity}');

    switch (command.action) {
      case VoiceCommandAction.buy:
        await _handleBuyCommand(command);
        break;
      case VoiceCommandAction.sell:
        await _handleSellCommand(command);
        break;
      case VoiceCommandAction.checkPortfolio:
        await _handlePortfolioCommand(command);
        break;
      case VoiceCommandAction.getQuote:
        await _handleQuoteCommand(command);
        break;
      case VoiceCommandAction.unknown:
        await _handleUnknownCommand(command);
        break;
    }
  }

  Future<void> _handleBuyCommand(VoiceCommand command) async {
    debugPrint(
        'VoiceCommandHandler: Handling buy command for ${command.symbol}');

    if (!command.isComplete) {
      final missing = command.missingParameters;
      debugPrint(
          'VoiceCommandHandler: Command incomplete, missing: ${missing.join(', ')}');
      final message =
          'I need more information. Please specify the ${missing.join(' and ')}.';
      await _voiceAssistant.speak(message);
      if (mounted) {
        context.read<ConversationBloc>().add(CommandResponseReceived(
              response: message,
              shouldSpeak: true,
            ));
      }
      return;
    }

    if (mounted) {
      // Ensure security list is loaded before searching
      await _ensureSecurityListLoaded();

      // Find the security to get the correct zen_id
      debugPrint(
          'VoiceCommandHandler: Searching for security with symbol: ${command.symbol}');
      final security = await _findSecurityBySymbol(command.symbol!);

      if (security == null) {
        debugPrint(
            'VoiceCommandHandler: Security not found for symbol: ${command.symbol}');
        final message =
            'Sorry, I couldn\'t find the stock ${command.symbol}. Please check the symbol and try again.';
        await _voiceAssistant.speak(message);
        if (mounted) {
          context.read<ConversationBloc>().add(CommandResponseReceived(
                response: message,
                shouldSpeak: true,
              ));
        }
        return;
      }

      debugPrint(
          'VoiceCommandHandler: Found security: ${security.name} with zen_id: ${security.zenId}');

      // Show account selection dialog
      await _voiceAssistant
          .speak('Please select your trading account to place the order.');
      debugPrint('VoiceCommandHandler: Showing account selection dialog');

      final selectionResult = await _showAccountSelectionDialog();
      debugPrint(
          'VoiceCommandHandler: Account selection result: $selectionResult');
      if (selectionResult == null) {
        await _voiceAssistant
            .speak('Order cancelled. Account selection is required.');
        debugPrint(
            'VoiceCommandHandler: Account selection cancelled, exiting buy flow.');
        context.read<ConversationBloc>().add(CommandResponseReceived(
              response: 'Order cancelled. Account selection is required.',
              shouldSpeak: true,
            ));
        return;
      }

      // Get auth state for client ID
      final authState = context.read<AuthBloc>().state;
      if (authState is! AuthAuthenticated) {
        await _voiceAssistant.speak('Authentication required to place orders.');
        if (mounted) {
          context.read<ConversationBloc>().add(CommandResponseReceived(
                response: 'Authentication required to place orders.',
                shouldSpeak: true,
              ));
        }
        return;
      }

      // Extract selection values
      final brokerName = selectionResult['brokerName'] as String?;
      final accountId = selectionResult['accountId'];
      final strategyId = selectionResult['strategyId'];

      // Handle "all" selections by using the first available option
      final credentials = authState.credentialsModel;
      int finalAccountId;
      int finalStrategyId;
      String finalBroker;

      if (accountId == "all" || accountId == null) {
        // Use first available account
        final firstBroker = credentials.brokers.first;
        finalAccountId = firstBroker.accounts.first.accountId;
        finalBroker = firstBroker.brokerName;
        finalStrategyId =
            firstBroker.accounts.first.strategies.first.strategyId;
      } else {
        finalAccountId =
            accountId is int ? accountId : int.parse(accountId.toString());
        finalBroker = brokerName ?? 'ZEN_BROKER';

        if (strategyId == "all" || strategyId == null) {
          // Find the account and use its first strategy
          final account = credentials.getStrategies(finalAccountId);
          finalStrategyId = account.first.strategyId;
        } else {
          finalStrategyId =
              strategyId is int ? strategyId : int.parse(strategyId.toString());
        }
      }

      // Create order using selected values
      final orderForm = OrderFormModel(
        clientId: credentials.clientId,
        accountId: finalAccountId,
        strategyId: finalStrategyId,
        broker: finalBroker,
        exchange: 'NSE', // TODO: Determine from symbol
        transactionType: 'BUY',
        quantity: command.quantity!,
        product: 'CNC', // TODO: Get from user preference
        validity: 'DAY',
        orderType:
            _convertOrderType(command.orderType ?? VoiceOrderType.market),
        methodType: 'POST',
        zenId: security.zenId,
      );

      debugPrint(
          'VoiceCommandHandler: Placing order with zen_id: ${security.zenId}, accountId: $finalAccountId, strategyId: $finalStrategyId');

      // Dispatch to OrdersBloc
      context.read<OrdersBloc>().add(PlaceOrderEvent(orderForm));

      await _voiceAssistant.speak(
          'Submitting your buy order for ${command.quantity} shares of ${security.name}. Your order has been placed.');
      context.read<ConversationBloc>().add(CommandResponseReceived(
            response:
                'Submitting your buy order for ${command.quantity} shares of ${security.name}. Your order has been placed.',
            shouldSpeak: false,
          ));
    }
  }

  Future<void> _handleSellCommand(VoiceCommand command) async {
    debugPrint(
        'VoiceCommandHandler: Handling sell command for ${command.symbol}');

    if (!command.isComplete) {
      final missing = command.missingParameters;
      debugPrint(
          'VoiceCommandHandler: Command incomplete, missing: ${missing.join(', ')}');
      final message =
          'I need more information. Please specify the ${missing.join(' and ')}.';
      await _voiceAssistant.speak(message);
      if (mounted) {
        context.read<ConversationBloc>().add(CommandResponseReceived(
              response: message,
              shouldSpeak: true,
            ));
      }
      return;
    }

    if (mounted) {
      // Ensure security list is loaded before searching
      await _ensureSecurityListLoaded();

      // Find the security to get the correct zen_id
      debugPrint(
          'VoiceCommandHandler: Searching for security with symbol: ${command.symbol}');
      final security = await _findSecurityBySymbol(command.symbol!);

      if (security == null) {
        debugPrint(
            'VoiceCommandHandler: Security not found for symbol: ${command.symbol}');
        final message =
            'Sorry, I couldn\'t find the stock ${command.symbol}. Please check the symbol and try again.';
        await _voiceAssistant.speak(message);
        if (mounted) {
          context.read<ConversationBloc>().add(CommandResponseReceived(
                response: message,
                shouldSpeak: true,
              ));
        }
        return;
      }

      debugPrint(
          'VoiceCommandHandler: Found security: ${security.name} with zen_id: ${security.zenId}');

      // Show account selection dialog
      await _voiceAssistant
          .speak('Please select your trading account to place the order.');

      final selectionResult = await _showAccountSelectionDialog();
      if (selectionResult == null) {
        await _voiceAssistant
            .speak('Order cancelled. Account selection is required.');
        debugPrint(
            'VoiceCommandHandler: Account selection cancelled, exiting sell flow.');
        if (mounted) {
          context.read<ConversationBloc>().add(CommandResponseReceived(
                response: 'Order cancelled. Account selection is required.',
                shouldSpeak: true,
              ));
        }
        return;
      }

      // Get auth state for client ID
      final authState = context.read<AuthBloc>().state;
      if (authState is! AuthAuthenticated) {
        await _voiceAssistant.speak('Authentication required to place orders.');
        if (mounted) {
          context.read<ConversationBloc>().add(CommandResponseReceived(
                response: 'Authentication required to place orders.',
                shouldSpeak: true,
              ));
        }
        return;
      }

      // Extract selection values
      final brokerName = selectionResult['brokerName'] as String?;
      final accountId = selectionResult['accountId'];
      final strategyId = selectionResult['strategyId'];

      // Handle "all" selections by using the first available option
      final credentials = authState.credentialsModel;
      int finalAccountId;
      int finalStrategyId;
      String finalBroker;

      if (accountId == "all" || accountId == null) {
        // Use first available account
        final firstBroker = credentials.brokers.first;
        finalAccountId = firstBroker.accounts.first.accountId;
        finalBroker = firstBroker.brokerName;
        finalStrategyId =
            firstBroker.accounts.first.strategies.first.strategyId;
      } else {
        finalAccountId =
            accountId is int ? accountId : int.parse(accountId.toString());
        finalBroker = brokerName ?? 'ZEN_BROKER';

        if (strategyId == "all" || strategyId == null) {
          // Find the account and use its first strategy
          final account = credentials.getStrategies(finalAccountId);
          finalStrategyId = account.first.strategyId;
        } else {
          finalStrategyId =
              strategyId is int ? strategyId : int.parse(strategyId.toString());
        }
      }

      // Create order using selected values
      final orderForm = OrderFormModel(
        clientId: credentials.clientId,
        accountId: finalAccountId,
        strategyId: finalStrategyId,
        broker: finalBroker,
        exchange: 'NSE', // TODO: Determine from symbol
        transactionType: 'SELL',
        quantity: command.quantity!,
        product: 'MIS', // TODO: Get from user preference
        validity: 'DAY',
        orderType:
            _convertOrderType(command.orderType ?? VoiceOrderType.market),
        methodType: 'POST',
        zenId: security.zenId,
      );

      debugPrint(
          'VoiceCommandHandler: Placing order with zen_id: ${security.zenId}, accountId: $finalAccountId, strategyId: $finalStrategyId');

      // Dispatch to OrdersBloc
      context.read<OrdersBloc>().add(PlaceOrderEvent(orderForm));

      await _voiceAssistant.speak(
          'Submitting your sell order for ${command.quantity} shares of ${security.name}. Your order has been placed.');
    }
  }

  Future<void> _handlePortfolioCommand(VoiceCommand command) async {
    await _voiceAssistant.speak('Opening your portfolio now.');

    // Trigger portfolio data fetch before navigation
    final authState = context.read<AuthBloc>().state;
    if (authState is AuthAuthenticated && mounted) {
      context
          .read<PortfolioBloc>()
          .add(FetchPortfolio(authState.credentialsModel.clientId));

      // Navigate to portfolio screen
      Navigator.of(context).pushNamed('/portfolio');
    } else if (mounted) {
      await _voiceAssistant
          .speak('Please log in first to view your portfolio.');
    }
  }

  Future<void> _handleQuoteCommand(VoiceCommand command) async {
    if (command.symbol == null) {
      await _voiceAssistant
          .speak('Please specify which stock you\'d like a quote for.');
      return;
    }

    await _voiceAssistant
        .speak('Getting the current price for ${command.symbol}.');

    try {
      // Find the security by symbol to get zen_id
      final security = await _findSecurityBySymbol(command.symbol!);

      if (security == null) {
        await _voiceAssistant.speak(
            'Sorry, I couldn\'t find the stock ${command.symbol}. Please check the symbol and try again.');
        return;
      }

      // Fetch the latest price
      final price = await _fetchLatestPrice(security.zenId);

      if (price != null) {
        await _voiceAssistant.speak(
            'The current price of ${security.name} is ${price.toStringAsFixed(2)} rupees.');
      } else {
        await _voiceAssistant.speak(
            'Sorry, I couldn\'t get the current price for ${command.symbol}. Please try again later.');
      }
    } catch (e) {
      debugPrint('Error getting quote: $e');
      await _voiceAssistant.speak(
          'Sorry, there was an error getting the price for ${command.symbol}. Please try again later.');
    }
  }

  Future<void> _handleUnknownCommand(VoiceCommand command) async {
    final message =
        'I didn\'t understand that command. You can say things like "buy 10 shares of TCS" or "check my portfolio".';
    await _voiceAssistant.speak(message);
    if (mounted) {
      context.read<ConversationBloc>().add(CommandResponseReceived(
            response: message,
            shouldSpeak: true,
          ));
    }
  }

  Future<void> _showIncompleteCommandDialog(VoiceCommand command) async {
    // Voice-first UX: no dialogs. Only TTS guidance.
    final missing = command.missingParameters;
    await _voiceAssistant.speak(
        'I need more information. Please specify the ${missing.join(' and ')}.');
  }

  Future<bool> _showOrderConfirmationDialog({
    required String action,
    required String symbol,
    required int quantity,
    required VoiceOrderType orderType,
  }) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Confirm $action Order'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Action: $action'),
            Text('Symbol: $symbol'),
            Text('Quantity: $quantity shares'),
            Text('Order Type: ${orderType.name}'),
            const SizedBox(height: 16),
            const Text(
              'Do you want to proceed with this order?',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Confirm'),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  String _convertOrderType(VoiceOrderType voiceOrderType) {
    switch (voiceOrderType) {
      case VoiceOrderType.market:
        return 'MARKET';
      case VoiceOrderType.limit:
        return 'LIMIT';
      case VoiceOrderType.stopLoss:
        return 'STANDALONE_SL_MARKET';
      case VoiceOrderType.unknown:
        return 'MARKET'; // Default to market order
    }
  }

  /// Show account selection dialog and return selected values
  Future<Map<String, dynamic>?> _showAccountSelectionDialog() async {
    debugPrint('VoiceCommandHandler: Showing account selection dialog');
    final authState = context.read<AuthBloc>().state;
    if (authState is! AuthAuthenticated) {
      await _voiceAssistant.speak('Please log in first to place orders.');
      return null;
    }

    final credentials = authState.credentialsModel;
    debugPrint(
        'VoiceCommandHandler: Showing account selection dialog for ${credentials.clientId}');

    // Show dialog with BlocProvider for BrokerAccountStrategySelectionBloc
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      barrierDismissible: false,
      builder: (context) => BlocProvider(
        create: (context) => BrokerAccountStrategySelectionBloc(credentials),
        child: BrokerAccountStrategyModal(
          showSetAsDefault: false,
          onDefaultSet: (clientId) {},
        ),
      ),
    );
    debugPrint('VoiceCommandHandler: Account selection result: $result');

    return result;
  }

  /// Find security by symbol using the security search service
  Future<SecurityModel?> _findSecurityBySymbol(String symbol) async {
    debugPrint('VoiceCommand: Searching for symbol: "$symbol"');

    final securityState = context.read<SecurityListBloc>().state;

    if (securityState is! SecurityListLoaded) {
      debugPrint('VoiceCommand: Security list not loaded');
      return null;
    }

    debugPrint(
        'VoiceCommand: Security list loaded - Equities: ${securityState.equityList.length}, F&O: ${securityState.featuresList.length}');

    // Try multiple search approaches for better matching

    // 1. Direct symbol matching in equities
    final directEquityMatch = securityState.equityList
        .where((s) => s.tradingSymbol.toUpperCase() == symbol.toUpperCase())
        .toList();

    if (directEquityMatch.isNotEmpty) {
      debugPrint(
          'VoiceCommand: Found direct equity match: ${directEquityMatch.first.tradingSymbol} (zen_id: ${directEquityMatch.first.zenId})');
      return directEquityMatch.first;
    }

    // // 2. Name matching in equities (for company names)
    // final nameEquityMatch = securityState.equityList
    //     .where((s) =>
    //         s.name.toUpperCase().contains(symbol.toUpperCase()) ||
    //         s.tradingSymbol.toUpperCase().contains(symbol.toUpperCase()))
    //     .toList();

    // if (nameEquityMatch.isNotEmpty) {
    //   debugPrint(
    //       'VoiceCommand: Found equity name match: ${nameEquityMatch.first.tradingSymbol} (${nameEquityMatch.first.name}) (zen_id: ${nameEquityMatch.first.zenId})');
    //   return nameEquityMatch.first;
    // }

    // // 3. Use the search service for equities
    // final equityResults = <SecurityModel>[];
    // SecurityListSearchService.optimizedSearch(
    //   symbol,
    //   context,
    //   'equity',
    //   () => equityResults.clear(),
    //   (results) => equityResults.addAll(results),
    // );

    // if (equityResults.isNotEmpty) {
    //   debugPrint(
    //       'VoiceCommand: Found equity via search service: ${equityResults.first.tradingSymbol} (zen_id: ${equityResults.first.zenId})');
    //   return equityResults.first;
    // }

    // // 4. Direct symbol matching in F&O
    // final directFnoMatch = securityState.featuresList
    //     .where((s) => s.tradingSymbol.toUpperCase() == symbol.toUpperCase())
    //     .toList();

    // if (directFnoMatch.isNotEmpty) {
    //   debugPrint(
    //       'VoiceCommand: Found direct F&O match: ${directFnoMatch.first.tradingSymbol} (zen_id: ${directFnoMatch.first.zenId})');
    //   return directFnoMatch.first;
    // }

    // // 5. Use the search service for F&O
    // final fnoResults = <SecurityModel>[];
    // SecurityListSearchService.optimizedSearch(
    //   symbol,
    //   context,
    //   'fno',
    //   () => fnoResults.clear(),
    //   (results) => fnoResults.addAll(results),
    // );

    // if (fnoResults.isNotEmpty) {
    //   debugPrint(
    //       'VoiceCommand: Found F&O via search service: ${fnoResults.first.tradingSymbol} (zen_id: ${fnoResults.first.zenId})');
    //   return fnoResults.first;
    // }

    // debugPrint('VoiceCommand: No matches found for symbol: "$symbol"');

    // // 6. Debug: Show some available symbols for troubleshooting
    // final sampleEquities = securityState.equityList
    //     .take(5)
    //     .map((s) => '${s.tradingSymbol} (${s.name})')
    //     .join(', ');
    // debugPrint('VoiceCommand: Sample available equities: $sampleEquities');

    return null;
  }

  /// Fetch latest price for a given zen_id
  Future<double?> _fetchLatestPrice(int zenId) async {
    try {
      final url = ApiPath.getLtpPrice(zenId);
      debugPrint('Fetching price from: $url');

      final response = await _httpService.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        debugPrint('Price response: $data');

        // Extract the latest price from the response
        if (data['latest_price'] != null) {
          return (data['latest_price'] as num).toDouble();
        }
      } else {
        debugPrint(
            'Failed to fetch price. Status code: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching latest price: $e');
    }

    return null;
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

import 'package:phoenix/models/conversation_message.dart';
import 'package:phoenix/models/voice_command_model.dart';
import 'package:phoenix/services/speech_recognition_service.dart';
import 'package:phoenix/services/voice_assistant_service.dart';
import 'conversation_event.dart';
import 'conversation_state.dart';

/// BLoC for managing conversational voice assistant state
class ConversationBloc extends Bloc<ConversationEvent, ConversationState> {
  final SpeechRecognitionService _speechService;
  final VoiceAssistantService _voiceService;
  
  StreamSubscription<VoiceCommand>? _voiceCommandSubscription;

  ConversationBloc({
    SpeechRecognitionService? speechService,
    VoiceAssistantService? voiceService,
  })  : _speechService = speechService ?? SpeechRecognitionService(),
        _voiceService = voiceService ?? VoiceAssistantService(),
        super(ConversationState.initial()) {
    
    // Register event handlers
    on<ConversationStarted>(_onConversationStarted);
    on<ConversationClosed>(_onConversationClosed);
    on<VoiceListeningStarted>(_onVoiceListeningStarted);
    on<VoiceListeningStopped>(_onVoiceListeningStopped);
    on<VoicePartialReceived>(_onVoicePartialReceived);
    on<VoiceFinalReceived>(_onVoiceFinalReceived);
    on<VoicePauseDetected>(_onVoicePauseDetected);
    on<VoiceFinalizingStarted>(_onVoiceFinalizingStarted);
    on<VoiceTimeout>(_onVoiceTimeout);
    on<VoiceCommandProcessed>(_onVoiceCommandProcessed);
    on<MessageAdded>(_onMessageAdded);
    on<MessageStatusUpdated>(_onMessageStatusUpdated);
    on<MessageSpoken>(_onMessageSpoken);
    on<TTSSpeakingStarted>(_onTTSSpeakingStarted);
    on<TTSSpeakingCompleted>(_onTTSSpeakingCompleted);
    on<VoiceRecognitionError>(_onVoiceRecognitionError);
    on<TTSError>(_onTTSError);
    on<CommandResponseReceived>(_onCommandResponseReceived);
    on<RetryLastCommand>(_onRetryLastCommand);
    on<ConversationHistoryCleared>(_onConversationHistoryCleared);
    on<SpeechRecognitionAvailabilityChanged>(_onSpeechRecognitionAvailabilityChanged);
    on<AssistantResponseAdded>(_onAssistantResponseAdded);
    on<MicrophoneToggled>(_onMicrophoneToggled);
    on<ConversationContextSaved>(_onConversationContextSaved);
    on<ConversationContextLoaded>(_onConversationContextLoaded);

    // Initialize services
    _initializeServices();
  }

  /// Initialize speech and voice services
  Future<void> _initializeServices() async {
    try {
      // Initialize voice assistant service
      await _voiceService.initialize();
      
      // Subscribe to voice commands
      _voiceCommandSubscription = _voiceService.commandStream.listen(
        (command) => add(VoiceCommandProcessed(command)),
      );

      // Initialize speech recognition and check availability
      final speechAvailable = await _speechService.initialize();
      add(SpeechRecognitionAvailabilityChanged(speechAvailable));
      
      debugPrint('ConversationBloc: Services initialized successfully');
    } catch (e) {
      debugPrint('ConversationBloc: Error initializing services - $e');
      add(VoiceRecognitionError('Failed to initialize voice services: $e'));
    }
  }

  /// Handle conversation started
  Future<void> _onConversationStarted(
    ConversationStarted event,
    Emitter<ConversationState> emit,
  ) async {
    final sessionId = const Uuid().v4();
    
    emit(state.copyWith(
      status: ConversationStatus.active,
      currentSessionId: sessionId,
      sessionStartTime: DateTime.now(),
      clearError: true,
    ));

    // Add welcome message
    final welcomeMessage = ConversationMessage.assistantResponse(
      content: 'Hello! I\'m your voice assistant. What would you like to do today?',
      isSpoken: true,
    );
    
    add(MessageAdded(welcomeMessage));
    add(MessageSpoken(welcomeMessage.id, welcomeMessage.content));
  }

  /// Handle conversation closed
  Future<void> _onConversationClosed(
    ConversationClosed event,
    Emitter<ConversationState> emit,
  ) async {
    // Stop any ongoing speech recognition
    await _speechService.stopListening();
    
    // Save conversation context before closing
    add(const ConversationContextSaved());
    
    emit(state.copyWith(
      status: ConversationStatus.closed,
      voiceStatus: VoiceListeningStatus.idle,
      clearPartialText: true,
      clearError: true,
      clearSpeakingMessage: true,
    ));
  }

  /// Handle voice listening started
  Future<void> _onVoiceListeningStarted(
    VoiceListeningStarted event,
    Emitter<ConversationState> emit,
  ) async {
    if (!state.canListen) {
      debugPrint('ConversationBloc: Cannot start listening - conditions not met');
      return;
    }

    emit(state.copyWith(
      voiceStatus: VoiceListeningStatus.listening,
      clearPartialText: true,
      clearError: true,
    ));

    try {
      final result = await _speechService.startListening(
        onPartial: (text) => add(VoicePartialReceived(text)),
        onFinal: (text) => add(VoiceFinalReceived(text)),
        onListening: () => {}, // Handled internally already
        onPauseDetected: () => add(const VoicePauseDetected()),
        onFinalizing: () => add(const VoiceFinalizingStarted()),
        onTimeout: () => add(const VoiceTimeout()),
      );

      if (result == null) {
        add(const VoiceRecognitionError('Failed to start speech recognition'));
      }
    } catch (e) {
      add(VoiceRecognitionError('Error starting speech recognition: $e'));
    }
  }

  /// Handle voice listening stopped
  Future<void> _onVoiceListeningStopped(
    VoiceListeningStopped event,
    Emitter<ConversationState> emit,
  ) async {
    await _speechService.stopListening();
    
    emit(state.copyWith(
      voiceStatus: VoiceListeningStatus.idle,
      clearPartialText: true,
    ));
  }

  /// Handle partial voice recognition result
  void _onVoicePartialReceived(
    VoicePartialReceived event,
    Emitter<ConversationState> emit,
  ) {
    emit(state.copyWith(currentPartialText: event.partialText));
  }

  /// Handle final voice recognition result
  Future<void> _onVoiceFinalReceived(
    VoiceFinalReceived event,
    Emitter<ConversationState> emit,
  ) async {
    final trimmed = event.finalText.trim();
    if (trimmed.isEmpty) {
      emit(state.copyWith(
        voiceStatus: VoiceListeningStatus.idle,
        clearPartialText: true,
      ));
      return;
    }

    // Reject finals that end with "of" or clearly lack a symbol after "of"
    if (RegExp(r'\bof\s*$', caseSensitive: false).hasMatch(trimmed)) {
      add(CommandResponseReceived(
        response: 'Please say the stock symbol after "of". For example: "buy 1 share of TCS".',
        shouldSpeak: true,
      ));
      emit(state.copyWith(
        voiceStatus: VoiceListeningStatus.idle,
        clearPartialText: true,
      ));
      return;
    }

    emit(state.copyWith(
      voiceStatus: VoiceListeningStatus.processing,
      clearPartialText: true,
    ));

    // Create user message
    final userMessage = ConversationMessage.userVoice(
      originalText: trimmed,
      processedText: trimmed,
    );

    add(MessageAdded(userMessage));

    // Parse and process the voice command
    try {
      final intent = _parseTextToVoiceCommand(event.finalText);

      // Session guard: if action requires a symbol and it's missing, don't emit a command
      final action = (intent['action'] as String?)?.toLowerCase() ?? 'unknown';
      final symbol = (intent['symbol'] as String?)?.trim();
      final quantity = intent['quantity'];
      final needsSymbol = action == 'buy' || action == 'sell';

      if (needsSymbol && (symbol == null || symbol.isEmpty)) {
        final sampleQty = (quantity is int && quantity > 0) ? quantity : 1;
        add(CommandResponseReceived(
          response: 'I need the stock symbol to $action. For example: "$action $sampleQty shares of TCS".',
          shouldSpeak: true,
        ));
        // Return to idle; user can retry with a complete command
        emit(state.copyWith(voiceStatus: VoiceListeningStatus.idle));
        return;
      }
      
      // Process the command through voice assistant service (only when sufficiently complete)
      await _voiceService.handleAssistantIntent(intent);
      
    } catch (e) {
      debugPrint('ConversationBloc: Error processing voice command: $e');
      add(CommandResponseReceived(
        response: 'Sorry, I couldn\'t understand that command. Please try again.',
        shouldSpeak: true,
      ));
    }
  }

  /// Handle voice command processed
  void _onVoiceCommandProcessed(
    VoiceCommandProcessed event,
    Emitter<ConversationState> emit,
  ) {
    emit(state.copyWith(lastProcessedCommand: event.command));
    
    // Generate response based on command
    final response = _generateCommandResponse(event.command);
    add(CommandResponseReceived(
      response: response,
      commandId: event.command.id,
      shouldSpeak: true,
    ));
  }

  /// Handle message added
  void _onMessageAdded(
    MessageAdded event,
    Emitter<ConversationState> emit,
  ) {
    emit(state.addMessage(event.message));
  }

  /// Handle message status updated
  void _onMessageStatusUpdated(
    MessageStatusUpdated event,
    Emitter<ConversationState> emit,
  ) {
    emit(state.updateMessageStatus(event.messageId, event.newStatus));
  }

  /// Handle message spoken
  Future<void> _onMessageSpoken(
    MessageSpoken event,
    Emitter<ConversationState> emit,
  ) async {
    add(TTSSpeakingStarted(event.messageId));
    
    try {
      await _voiceService.speak(event.text);
      add(TTSSpeakingCompleted(event.messageId));
    } catch (e) {
      add(TTSError('Error speaking message: $e'));
    }
  }

  /// Handle TTS speaking started
  void _onTTSSpeakingStarted(
    TTSSpeakingStarted event,
    Emitter<ConversationState> emit,
  ) {
    emit(state.copyWith(
      voiceStatus: VoiceListeningStatus.speaking,
      isTTSSpeaking: true,
      currentSpeakingMessageId: event.messageId,
    ));
  }

  /// Handle TTS speaking completed
  void _onTTSSpeakingCompleted(
    TTSSpeakingCompleted event,
    Emitter<ConversationState> emit,
  ) {
    emit(state.copyWith(
      voiceStatus: VoiceListeningStatus.idle,
      isTTSSpeaking: false,
      clearSpeakingMessage: true,
    ));
  }

  /// Handle voice recognition error
  void _onVoiceRecognitionError(
    VoiceRecognitionError event,
    Emitter<ConversationState> emit,
  ) {
    emit(state.copyWith(
      voiceStatus: VoiceListeningStatus.error,
      errorMessage: event.error,
    ));

    // Add error message to conversation
    final errorMessage = ConversationMessage.error(
      content: event.error,
    );
    add(MessageAdded(errorMessage));
  }

  /// Handle TTS error
  void _onTTSError(
    TTSError event,
    Emitter<ConversationState> emit,
  ) {
    emit(state.copyWith(
      voiceStatus: VoiceListeningStatus.error,
      errorMessage: event.error,
      isTTSSpeaking: false,
      clearSpeakingMessage: true,
    ));
  }

  /// Handle command response received
  void _onCommandResponseReceived(
    CommandResponseReceived event,
    Emitter<ConversationState> emit,
  ) {
    // Create assistant response message
    final responseMessage = ConversationMessage.assistantResponse(
      content: event.response,
      isSpoken: event.shouldSpeak,
      voiceCommandId: event.commandId,
    );

    add(MessageAdded(responseMessage));

    if (event.shouldSpeak) {
      add(MessageSpoken(responseMessage.id, event.response));
    } else {
      // If not speaking, return to idle state
      emit(state.copyWith(voiceStatus: VoiceListeningStatus.idle));
    }
  }

  /// Handle retry last command
  void _onRetryLastCommand(
    RetryLastCommand event,
    Emitter<ConversationState> emit,
  ) {
    if (state.lastProcessedCommand != null) {
      add(VoiceCommandProcessed(state.lastProcessedCommand!));
    }
  }

  /// Handle conversation history cleared
  void _onConversationHistoryCleared(
    ConversationHistoryCleared event,
    Emitter<ConversationState> emit,
  ) {
    // clear current message
    
    emit(state.clearMessages());
  }

  /// Handle speech recognition availability changed
  void _onSpeechRecognitionAvailabilityChanged(
    SpeechRecognitionAvailabilityChanged event,
    Emitter<ConversationState> emit,
  ) {
    emit(state.copyWith(isSpeechRecognitionAvailable: event.isAvailable));
  }

  /// Handle assistant response added
  void _onAssistantResponseAdded(
    AssistantResponseAdded event,
    Emitter<ConversationState> emit,
  ) {
    final responseMessage = ConversationMessage.assistantResponse(
      content: event.response,
      isSpoken: event.shouldSpeak,
      voiceCommandId: event.relatedCommandId,
    );

    add(MessageAdded(responseMessage));

    if (event.shouldSpeak) {
      add(MessageSpoken(responseMessage.id, event.response));
    }
  }

  /// Handle microphone toggled
  void _onMicrophoneToggled(
    MicrophoneToggled event,
    Emitter<ConversationState> emit,
  ) {
    final newMutedState = !state.isMicrophoneMuted;
    
    if (newMutedState && state.isListening) {
      add(const VoiceListeningStopped());
    }
    
    emit(state.copyWith(isMicrophoneMuted: newMutedState));
  }

  /// Handle conversation context saved
  Future<void> _onConversationContextSaved(
    ConversationContextSaved event,
    Emitter<ConversationState> emit,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final contextData = {
        'sessionId': state.currentSessionId,
        'startTime': state.sessionStartTime?.toIso8601String(),
        'messages': state.messages.map((msg) => msg.toJson()).toList(),
      };
      
      await prefs.setString('conversation_context', jsonEncode(contextData));
      debugPrint('ConversationBloc: Context saved successfully');
    } catch (e) {
      debugPrint('ConversationBloc: Error saving context - $e');
    }
  }

  /// Handle conversation context loaded
  Future<void> _onConversationContextLoaded(
    ConversationContextLoaded event,
    Emitter<ConversationState> emit,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final contextJson = prefs.getString('conversation_context');
      
      if (contextJson != null) {
        final contextData = jsonDecode(contextJson) as Map<String, dynamic>;
        final messages = (contextData['messages'] as List?)
            ?.map((msgJson) => ConversationMessage.fromJson(msgJson))
            .toList() ?? [];
        
        emit(state.copyWith(
          currentSessionId: contextData['sessionId'] as String?,
          sessionStartTime: contextData['startTime'] != null 
              ? DateTime.parse(contextData['startTime'] as String)
              : null,
          messages: messages,
        ));
        
        debugPrint('ConversationBloc: Context loaded successfully');
      }
    } catch (e) {
      debugPrint('ConversationBloc: Error loading context - $e');
    }
  }

  /// Generate response for a voice command
  String _generateCommandResponse(VoiceCommand command) {
    switch (command.action) {
      case VoiceCommandAction.buy:
        if (command.symbol != null && command.quantity != null) {
          return 'I\'ll place a buy order for ${command.quantity} shares of ${command.symbol}. Please confirm this trade in the app.';
        } else {
          return 'I need more information. Please specify the stock symbol and quantity to buy.';
        }
      
      case VoiceCommandAction.sell:
        if (command.symbol != null && command.quantity != null) {
          return 'I\'ll place a sell order for ${command.quantity} shares of ${command.symbol}. Please confirm this trade in the app.';
        } else {
          return 'I need more information. Please specify the stock symbol and quantity to sell.';
        }
      
      case VoiceCommandAction.checkPortfolio:
        return 'Let me check your portfolio. You can view your current holdings in the portfolio section.';
      
      case VoiceCommandAction.getQuote:
        if (command.symbol != null) {
          return 'Let me get the current price for ${command.symbol}. Check the app for the latest quote.';
        } else {
          return 'Please specify which stock symbol you\'d like a quote for.';
        }
      
      case VoiceCommandAction.unknown:
        return 'I didn\'t understand that command. Try saying something like "Buy/Sell 10 shares of TCS"';
    }
  }

  /// Parse text to voice command (reused from overlay)
  Map<String, dynamic> _parseTextToVoiceCommand(String text) {
    final preprocessedText = _preprocessText(text);
    final lower = preprocessedText.toLowerCase();

    String action = 'unknown';
    action = _detectAction(lower);
    
    final symbol = _extractSymbol(preprocessedText);
    final quantity = _extractQuantity(preprocessedText);

    return <String, dynamic>{
      'action': action,
      'symbol': symbol,
      'quantity': quantity,
      'type': 'market',
      'originalText': text,
    };
  }

  /// Preprocess text to fix common speech recognition errors
  String _preprocessText(String text) {
    String processed = text;
    
    final corrections = {
      r'\bby\b(?=\s+\d)': 'buy',
      r'\bbye\b(?=\s+\d)': 'buy',
      r'\bbi\b(?=\s+\d)': 'buy',
      r'\bcell\b(?=\s+\d)': 'sell',
      r'\bsal\b(?=\s+\d)': 'sell',
      r'\bto\b(?=\s+shares?)': '2',
      r'\btoo\b(?=\s+shares?)': '2',
      r'\bfor\b(?=\s+shares?)': '4',
      r'\bfore\b(?=\s+shares?)': '4',
      r'\bate\b(?=\s+shares?)': '8',
      r'\btc\s*s\b': 'TCS',
      r'\bt\s*c\s*s\b': 'TCS',
      r'\btata\s*consultancy\b': 'TCS',
    };
    
    for (final entry in corrections.entries) {
      processed = processed.replaceAll(RegExp(entry.key, caseSensitive: false), entry.value);
    }
    
    return processed;
  }

  /// Enhanced action detection
  String _detectAction(String lowerText) {
    if (lowerText.contains('buy ') || lowerText.startsWith('buy') || lowerText.endsWith(' buy')) {
      return 'buy';
    }
    if (lowerText.contains('sell ') || lowerText.startsWith('sell') || lowerText.endsWith(' sell')) {
      return 'sell';
    }
    
    if (lowerText.contains(' by ') || lowerText.startsWith('by ')) {
      if (RegExp(r'\bby\s+(\d+|\w+)\s*(shares?|stocks?|of|tcs|infosys|reliance)', caseSensitive: false).hasMatch(lowerText)) {
        return 'buy';
      }
    }
    
    final buyAlternatives = ['by', 'bye', 'bi', 'bai', 'bui'];
    for (final alt in buyAlternatives) {
      final pattern = RegExp(r'\b' + alt + r'\s+(\d+|\w+)\s*(shares?|stocks?|of)', caseSensitive: false);
      if (pattern.hasMatch(lowerText)) {
        return 'buy';
      }
    }
    
    if (lowerText.contains('portfolio') || lowerText.contains('holdings') || lowerText.contains('my stocks')) {
      return 'checkPortfolio';
    }
    if (lowerText.contains('quote') || lowerText.contains('price') || lowerText.contains('cost') || lowerText.contains('rate')) {
      return 'getQuote';
    }
    
    return 'unknown';
  }

  String? _extractSymbol(String command) {
    final lowerCommand = command.toLowerCase().trim();

    // Prefer explicit pattern: "of <symbol>" to avoid picking up number words like "one"
    final ofPattern = RegExp(r'\bof\s+([a-zA-Z\.&\s]{2,30})$');
    final ofMatch = ofPattern.firstMatch(lowerCommand);

    // Common mappings for company names and spaced variants
    final symbolMap = {
      'apple': 'AAPL',
      'microsoft': 'MSFT',
      'tesla': 'TSLA',
      'google': 'GOOGL',
      'amazon': 'AMZN',
      'meta': 'META',
      'netflix': 'NFLX',
      'nvidia': 'NVDA',
      'amd': 'AMD',
      'tcs': 'TCS',
      't c s': 'TCS',
      't.c.s': 'TCS',
      'tata consultancy services': 'TCS',
      'tata consultancy': 'TCS',
      'infosys': 'INFY',
      'wipro': 'WIPRO',
      'reliance': 'RELIANCE',
      'hdfc bank': 'HDFCBANK',
      'hdfc': 'HDFCBANK',
      'icici bank': 'ICICIBANK',
      'icici': 'ICICIBANK',
    };

    String? mapToSymbol(String text) {
      final key = text.toLowerCase().trim();
      for (final entry in symbolMap.entries) {
        if (key == entry.key) return entry.value;
      }
      return null;
    }

    if (ofMatch != null) {
      final afterOf = ofMatch.group(1)!.trim();
      // Handle spaced variants like "t c s"
      final normalized = afterOf.replaceAll(RegExp(r'\s+'), ' ');
      final mapped = mapToSymbol(normalized);
      if (mapped != null) return mapped;

      // If not in map, pick the last token as a likely symbol (e.g., "of TCS")
      final lastToken = normalized.split(' ').where((t) => t.isNotEmpty).last.toUpperCase();
      // Disallow number words and generic words
      const banned = {
        'OF','THE','AND','FOR','WITH','THIS','THAT','FROM','INTO',
        'ONE','TWO','THREE','FOUR','FIVE','SIX','SEVEN','EIGHT','NINE','TEN'
      };
      if (lastToken.length >= 2 && lastToken.length <= 10 && !banned.contains(lastToken)) {
        return lastToken;
      }
      // If nothing valid after "of", treat as unknown
      return null;
    }

    // Fallback: try to find any known company names anywhere in the text
    for (final entry in symbolMap.entries) {
      if (lowerCommand.contains(entry.key)) {
        return entry.value;
      }
    }

    // Final fallback: scan tokens but exclude number words and generic terms
    final symbolRegex = RegExp(r'\b([a-zA-Z]{2,10})\b');
    final matches = symbolRegex.allMatches(command);
    const bannedTokens = {
      'BUY','SELL','SHARES','SHARE','STOCK','STOCKS','THE','AND','FOR','WITH','THIS','THAT','FROM','INTO',
      'ONE','TWO','THREE','FOUR','FIVE','SIX','SEVEN','EIGHT','NINE','TEN'
    };
    for (final match in matches) {
      final token = match.group(1)!.toUpperCase();
      if (!bannedTokens.contains(token)) {
        return token;
      }
    }

    return null;
  }

  int? _extractQuantity(String command) {
    final lowerCommand = command.toLowerCase();
    
    final quantityRegex = RegExp(r'(\d+)\s*(?:shares?|stocks?)', caseSensitive: false);
    final match = quantityRegex.firstMatch(lowerCommand);
    if (match != null) {
      return int.tryParse(match.group(1)!);
    }
    
    final spokenNumbers = {
      'one': 1, 'two': 2, 'three': 3, 'four': 4, 'five': 5,
      'six': 6, 'seven': 7, 'eight': 8, 'nine': 9, 'ten': 10,
      'too': 2, 'to': 2, 'for': 4, 'fore': 4, 'ate': 8,
    };
    
    for (final entry in spokenNumbers.entries) {
      if (lowerCommand.contains(entry.key)) {
        return entry.value;
      }
    }
    
    final numberRegex = RegExp(r'\b(\d+)\b');
    final numberMatch = numberRegex.firstMatch(command);
    if (numberMatch != null) {
      return int.tryParse(numberMatch.group(1)!);
    }
    
    return null;
  }

  /// Handle voice pause detected
  void _onVoicePauseDetected(
    VoicePauseDetected event,
    Emitter<ConversationState> emit,
  ) {
    emit(state.copyWith(voiceStatus: VoiceListeningStatus.paused));
  }

  /// Handle voice finalizing started
  void _onVoiceFinalizingStarted(
    VoiceFinalizingStarted event,
    Emitter<ConversationState> emit,
  ) {
    emit(state.copyWith(voiceStatus: VoiceListeningStatus.finalizing));
  }

  /// Handle voice timeout
  Future<void> _onVoiceTimeout(
    VoiceTimeout event,
    Emitter<ConversationState> emit,
  ) async {
    // If we had partial input, show a timeout message
    if (state.currentPartialText != null && state.currentPartialText!.isNotEmpty) {
      add(CommandResponseReceived(
        response: 'I didn\'t catch the complete command. Please try speaking again.',
        shouldSpeak: true,
      ));
    }
    
    // Return to idle state
    emit(state.copyWith(
      voiceStatus: VoiceListeningStatus.idle,
      clearPartialText: true,
    ));
  }

  @override
  Future<void> close() {
    _voiceCommandSubscription?.cancel();
    return super.close();
  }
}

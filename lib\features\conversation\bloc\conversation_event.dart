import 'package:equatable/equatable.dart';
import 'package:phoenix/models/conversation_message.dart';
import 'package:phoenix/models/voice_command_model.dart';

/// Base class for all conversation events
abstract class ConversationEvent extends Equatable {
  const ConversationEvent();

  @override
  List<Object?> get props => [];
}

/// Event to start a new conversation session
class ConversationStarted extends ConversationEvent {
  const ConversationStarted();
}

/// Event to close/end the conversation session
class ConversationClosed extends ConversationEvent {
  const ConversationClosed();
}

/// Event to start listening for voice input
class VoiceListeningStarted extends ConversationEvent {
  const VoiceListeningStarted();
}

/// Event to stop listening for voice input
class VoiceListeningStopped extends ConversationEvent {
  const VoiceListeningStopped();
}

/// Event when partial speech recognition result is received
class VoicePartialReceived extends ConversationEvent {
  final String partialText;

  const VoicePartialReceived(this.partialText);

  @override
  List<Object?> get props => [partialText];
}

/// Event when final speech recognition result is received
class VoiceFinalReceived extends ConversationEvent {
  final String finalText;

  const VoiceFinalReceived(this.finalText);

  @override
  List<Object?> get props => [finalText];
}

/// Event to process a voice command
class VoiceCommandProcessed extends ConversationEvent {
  final VoiceCommand command;

  const VoiceCommandProcessed(this.command);

  @override
  List<Object?> get props => [command];
}

/// Event when a message is added to the conversation
class MessageAdded extends ConversationEvent {
  final ConversationMessage message;

  const MessageAdded(this.message);

  @override
  List<Object?> get props => [message];
}

/// Event to update a message status
class MessageStatusUpdated extends ConversationEvent {
  final String messageId;
  final MessageStatus newStatus;

  const MessageStatusUpdated(this.messageId, this.newStatus);

  @override
  List<Object?> get props => [messageId, newStatus];
}

/// Event to speak a message using text-to-speech
class MessageSpoken extends ConversationEvent {
  final String messageId;
  final String text;

  const MessageSpoken(this.messageId, this.text);

  @override
  List<Object?> get props => [messageId, text];
}

/// Event when TTS speaking starts
class TTSSpeakingStarted extends ConversationEvent {
  final String messageId;

  const TTSSpeakingStarted(this.messageId);

  @override
  List<Object?> get props => [messageId];
}

/// Event when TTS speaking completes
class TTSSpeakingCompleted extends ConversationEvent {
  final String messageId;

  const TTSSpeakingCompleted(this.messageId);

  @override
  List<Object?> get props => [messageId];
}

/// Event when an error occurs during speech recognition
class VoiceRecognitionError extends ConversationEvent {
  final String error;

  const VoiceRecognitionError(this.error);

  @override
  List<Object?> get props => [error];
}

/// Event when an error occurs during TTS
class TTSError extends ConversationEvent {
  final String error;

  const TTSError(this.error);

  @override
  List<Object?> get props => [error];
}

/// Event when voice command processing results in a response
class CommandResponseReceived extends ConversationEvent {
  final String response;
  final String? commandId;
  final bool shouldSpeak;

  const CommandResponseReceived({
    required this.response,
    this.commandId,
    this.shouldSpeak = true,
  });

  @override
  List<Object?> get props => [response, commandId, shouldSpeak];
}

/// Event to retry the last failed voice command
class RetryLastCommand extends ConversationEvent {
  const RetryLastCommand();
}

/// Event to clear the conversation history
class ConversationHistoryCleared extends ConversationEvent {
  const ConversationHistoryCleared();
}

/// Event when speech recognition becomes available/unavailable
class SpeechRecognitionAvailabilityChanged extends ConversationEvent {
  final bool isAvailable;

  const SpeechRecognitionAvailabilityChanged(this.isAvailable);

  @override
  List<Object?> get props => [isAvailable];
}

/// Event to manually add an assistant response
class AssistantResponseAdded extends ConversationEvent {
  final String response;
  final bool shouldSpeak;
  final String? relatedCommandId;

  const AssistantResponseAdded({
    required this.response,
    this.shouldSpeak = true,
    this.relatedCommandId,
  });

  @override
  List<Object?> get props => [response, shouldSpeak, relatedCommandId];
}

/// Event to toggle microphone mute state
class MicrophoneToggled extends ConversationEvent {
  const MicrophoneToggled();
}

/// Event when conversation context needs to be saved
class ConversationContextSaved extends ConversationEvent {
  const ConversationContextSaved();
}

/// Event when conversation context needs to be loaded
class ConversationContextLoaded extends ConversationEvent {
  const ConversationContextLoaded();
}
/// Event when pause is detected during voice input
class VoicePauseDetected extends ConversationEvent {
  const VoicePauseDetected();
}

/// Event when finalizing voice input after extended pause
class VoiceFinalizingStarted extends ConversationEvent {
  const VoiceFinalizingStarted();
}

/// Event when voice input times out
class VoiceTimeout extends ConversationEvent {
  const VoiceTimeout();
}
